{"name": "lightning", "version": "3.8.2", "description": "lightning", "author": "lightning", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "pro": "vue-cli-service build", "build:uat": "vue-cli-service build --mode uat", "uat": "npm run build:uat", "build:int": "vue-cli-service build --mode int", "int": "npm run build:int", "build:test": "vue-cli-service build --mode test", "test": "npm run build:test", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "dependencies": {"@babel/plugin-syntax-dynamic-import": "^7.8.3", "@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "diagram-js": "^5.0.0", "echarts": "^5.4.2", "element-ui": "2.15.8", "file-saver": "2.0.5", "form-gen-parser": "^1.0.3", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "lightning": "file:", "moment": "^2.29.1", "nprogress": "0.2.0", "power-workflow": "0.0.17", "quill": "1.3.7", "rxjs": "7.5.6", "screenfull": "5.0.2", "sortablejs": "1.10.2", "vkbeautify": "^0.99.3", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-i18n": "^8.18.2", "vue-json-viewer": "^2.2.22", "vue-meta": "2.4.0", "vue-monaco-editor": "0.0.19", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "webpack-bundle-analyzer": "^4.10.2", "workflow-bpmn-modeler": "^0.2.8"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}