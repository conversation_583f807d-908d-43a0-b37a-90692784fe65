# app and job
*.class
*.settings
*.project
*.classpath
*/.settings
*/target
target/
*/bin
/.idea
*.iml
*/LOG_PATH_IS_UNDEFINED


# web
.DS_Store
node_modules/
dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
**/*.log

tests/**/coverage/
tests/e2e/reports
selenium-debug.log

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.local

package-lock.json
yarn.lock
/.classpath
/.project
pnpm-lock.yaml