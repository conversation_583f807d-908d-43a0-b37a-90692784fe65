### 任务列表： TASK0001 
帮我生成UC0001的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理

### 任务列表： TASK0002
帮我生成cost_program_design.md文档中UC0002的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理

### 任务列表： TASK0003
帮我生成cost_program_design.md文档中UC0003的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能实现，使用这个类import com.xl.alm.app.util.ExcelUtil;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理

### 任务列表： TASK0004
帮我生成cost_program_design.md文档中UC0004的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 不要漏掉表中的任何一个字段
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能使用这个类import com.xl.alm.app.util.ExcelUtil;不要@excel注解
* 使用这2个工具类 import com.jd.lightning.common.utils.DateUtils; import com.jd.lightning.common.utils.StringUtils;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理

### 任务列表： TASK0005
帮我生成cost_program_design.md文档中4.1.3.1.3.5 导入会计准备金明细数据(UC0005)的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能使用这个类import com.xl.alm.app.util.ExcelUtil;不要@excel注解
* 使用这2个工具类 import com.jd.lightning.common.utils.DateUtils; import com.jd.lightning.common.utils.StringUtils;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理

### 任务列表： TASK0006
帮我生成cost_program_design.md文档中4.1.3.1.3.6 导入法定准备金预测数据(UC0006)的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能使用这个类import com.xl.alm.app.util.ExcelUtil;不要@excel注解
* 使用这2个工具类 import com.jd.lightning.common.utils.DateUtils; import com.jd.lightning.common.utils.StringUtils;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理



### 任务列表： TASK0007
帮我生成cost_program_design.md文档中###### 4.1.3.1.3.7 导入负债现金流数据(UC0007)的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能使用这个类import com.xl.alm.app.util.ExcelUtil;不要@excel注解。excel表头包含全部表字段，不要有遗漏
* 使用这2个工具类 import com.jd.lightning.common.utils.DateUtils; import com.jd.lightning.common.utils.StringUtils;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理


### 任务列表： TASK0008
帮我生成cost_program_design.md文档中,表名是t_cost_product_statistics的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能使用这个类import com.xl.alm.app.util.ExcelUtil;不要@excel注解。excel表头包含全部表字段，不要有遗漏
* 使用这2个工具类 import com.jd.lightning.common.utils.DateUtils; import com.jd.lightning.common.utils.StringUtils;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理


### 任务列表： TASK0009
帮我生成cost_program_design.md文档中,表名是t_cost_business_type_summary的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能使用这个类import com.xl.alm.app.util.ExcelUtil;不要@excel注解。excel表头包含全部表字段，不要有遗漏
* 使用这2个工具类 import com.jd.lightning.common.utils.DateUtils; import com.jd.lightning.common.utils.StringUtils;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理


### 任务列表： TASK0010
帮我生成cost_program_design.md文档中,表名是t_cost_account_summary的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能使用这个类import com.xl.alm.app.util.ExcelUtil;不要@excel注解。excel表头包含全部表字段，不要有遗漏
* 使用这2个工具类 import com.jd.lightning.common.utils.DateUtils; import com.jd.lightning.common.utils.StringUtils;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理


### 任务列表： TASK0011
帮我生成cost_program_design.md文档中,表名是t_cost_product_effective_rate的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能使用这个类import com.xl.alm.app.util.ExcelUtil;不要@excel注解。excel表头包含全部表字段，不要有遗漏
* 使用这2个工具类 import com.jd.lightning.common.utils.DateUtils; import com.jd.lightning.common.utils.StringUtils;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理


### 任务列表： TASK0012
帮我生成cost_program_design.md文档中,表名是t_cost_account_effective_rate的代码
## 菜单sql
按照ruoyi框架，生成功能菜单sql,格式按照universal_avg_settlement_rate_menu.sql文件里面的格式
## 后端部分：
* 后端在app下面,采用springboot2.7.18
* RESTful API端点实现了完整的CRUD操作
* 按照 app_rule.md 规范的包结构组织
* controller/service/mapper/entity的标准命名约定
* Follows the same structure as other mapper files like SampleMapper.xml
* The controller follows the same RESTful API design and security patterns as the original  SampleController.java
* excel功能使用这个类import com.xl.alm.app.util.ExcelUtil;不要@excel注解。excel表头包含全部表字段，不要有遗漏
* 使用这2个工具类 import com.jd.lightning.common.utils.DateUtils; import com.jd.lightning.common.utils.StringUtils;
## 前端部分：
* 前端在web工程下，采用VUE 2.6.12
* Vue组件遵循指定的结构规范
* API模块遵循标准格式
* 采用BEM命名规范的SCSS样式
* 按照 web_rule.md 的标准文件结构
* 使用可复用组件实现通用功能
* 字段如果有值域则初始化字典数据
## 代码实现了所有要求的功能：
* 负债现金流数据的列表视图
* 添加/编辑/删除操作
* 导入/导出功能(在导入弹出框中有模版下载链接)
* 搜索和分页功能
* 适当的错误处理和加载状态管理