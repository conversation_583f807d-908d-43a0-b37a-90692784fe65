-- 成本管理模块数据库表结构

-- 1. 产品属性表(TB0001)
CREATE TABLE t_base_product_attribute (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    actuarial_code VARCHAR(20) NOT NULL COMMENT '精算代码',
    business_code VARCHAR(20) NOT NULL COMMENT '业务代码',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    term_type CHAR(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识：L-长期，S-短期',
    insurance_main_type VARCHAR(50) NOT NULL COMMENT '险种主类',
    insurance_sub_type VARCHAR(50) NOT NULL COMMENT '险种细类',
    design_type VARCHAR(50) NOT NULL COMMENT '设计类型',
    short_term_flag CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否中短：Y-是，N-否',
    reg_mid_id CHAR(1) NOT NULL DEFAULT 'N' COMMENT '报监管中短标识：Y-是，N-否',
    guaranteed_cost_rate DECIMAL(10,6) COMMENT '定价保证成本率',
    sub_account VARCHAR(50) COMMENT '子账户',
    new_business_flag CHAR(1) DEFAULT 'Y' COMMENT '新业务标识：Y-是，N-否',
    remark VARCHAR(500) COMMENT '备注',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_product_attribute_unique (accounting_period, actuarial_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品属性表';

-- 2. 万能平均结算利率表(TB0002)
CREATE TABLE t_base_universal_avg_settlement_rate (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    actuarial_code VARCHAR(20) NOT NULL COMMENT '精算代码',
    business_code VARCHAR(20) NOT NULL COMMENT '业务代码',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    short_term_flag CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否中短：Y-是，N-否',
    guaranteed_cost_rate DECIMAL(10,10) DEFAULT 0 COMMENT '定价保证成本率',
    avg_rate_t0 DECIMAL(10,10) DEFAULT 0 COMMENT '平均结息利率T0',
    avg_rate_t1 DECIMAL(10,10) DEFAULT 0 COMMENT '平均结息利率T1',
    avg_rate_t2 DECIMAL(10,10) DEFAULT 0 COMMENT '平均结息利率T2',
    avg_rate_t3 DECIMAL(10,10) DEFAULT 0 COMMENT '平均结息利率T3',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_universal_rate_unique (accounting_period, actuarial_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='万能平均结算利率表';

-- 3. 分红方案表(TB0003)
CREATE TABLE t_base_dividend_fund_cost_rate (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    actuarial_code VARCHAR(20) NOT NULL COMMENT '精算代码',
    business_code VARCHAR(20) NOT NULL COMMENT '业务代码',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    short_term_flag CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否中短：Y-是，N-否',
    guaranteed_cost_rate DECIMAL(10,10) DEFAULT 0 COMMENT '定价保证成本率',
    investment_return_rate DECIMAL(10,10) DEFAULT 0 COMMENT '投资收益率假设',
    dividend_ratio DECIMAL(10,10) DEFAULT 0 COMMENT '分红比例',
    fund_cost_rate_t0 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T0',
    fund_cost_rate_t1 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T1',
    fund_cost_rate_t2 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T2',
    fund_cost_rate_t3 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T3',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_dividend_rate_unique (accounting_period, actuarial_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分红方案表';

-- 4. 法定准备金明细表(TB0004)
CREATE TABLE t_base_statutory_reserve_detail (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    actuarial_code VARCHAR(20) NOT NULL COMMENT '精算代码',
    business_code VARCHAR(20) NOT NULL COMMENT '业务代码',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    term_type CHAR(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识：L-长期，S-短期',
    design_type VARCHAR(50) NOT NULL COMMENT '设计类型',
    short_term_flag CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否中短：Y-是，N-否',
    valid_policy_count INT(11) DEFAULT 0 COMMENT '有效保单件数',
    accumulated_premium DECIMAL(18,10) DEFAULT 0 COMMENT '存量累计规模保费',
    account_value DECIMAL(18,10) DEFAULT 0 COMMENT '账户价值',
    statutory_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '法定/非单位准备金',
    guaranteed_rate_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '保证利率准备金',
    lapsed_policy_value DECIMAL(18,10) DEFAULT 0 COMMENT '失效单现价',
    waiver_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '豁免责任准备金',
    unmodeled_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '未建模准备金',
    persistence_bonus_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '持续奖准备金',
    long_term_unearned DECIMAL(18,10) DEFAULT 0 COMMENT '长期未到期准备金',
    short_term_unearned DECIMAL(18,10) DEFAULT 0 COMMENT '短险未到期准备金',
    unearned_premium_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '未到期责任准备金',
    reported_unpaid DECIMAL(18,10) DEFAULT 0 COMMENT '已报未决赔款',
    incurred_unreported DECIMAL(18,10) DEFAULT 0 COMMENT '未报未决赔款',
    claim_expense_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '理赔费用准备金',
    outstanding_claim_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '未决赔款准备金',
    total_statutory_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金合计',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_statutory_reserve_unique (accounting_period, actuarial_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='法定准备金明细表';

-- 5. 会计准备金明细表(TB0005)
CREATE TABLE t_base_accounting_reserve_detail (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    actuarial_code VARCHAR(20) NOT NULL COMMENT '精算代码',
    business_code VARCHAR(20) NOT NULL COMMENT '业务代码',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    term_type CHAR(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识：L-长期，S-短期',
    design_type VARCHAR(50) NOT NULL COMMENT '设计类型',
    short_term_flag CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否中短：Y-是，N-否',
    valid_policy_count INT(11) DEFAULT 0 COMMENT '有效保单件数',
    accumulated_premium DECIMAL(18,10) DEFAULT 0 COMMENT '存量累计规模保费',
    account_value DECIMAL(18,10) DEFAULT 0 COMMENT '账户价值',
    dividend_provision DECIMAL(18,10) DEFAULT 0 COMMENT '红利预提',
    best_estimate DECIMAL(18,10) DEFAULT 0 COMMENT '最优估计',
    risk_margin DECIMAL(18,10) DEFAULT 0 COMMENT '风险边际',
    residual_margin DECIMAL(18,10) DEFAULT 0 COMMENT '剩余边际',
    unmodeled_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '未建模准备金',
    waiver_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '豁免准备金',
    persistence_bonus_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '持续奖准备金',
    long_term_unearned DECIMAL(18,10) DEFAULT 0 COMMENT '长期险未到期',
    short_term_unearned DECIMAL(18,10) DEFAULT 0 COMMENT '短险未到期',
    unearned_premium_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '未到期责任准备金',
    reported_unpaid DECIMAL(18,10) DEFAULT 0 COMMENT '已报未决',
    incurred_unreported DECIMAL(18,10) DEFAULT 0 COMMENT '未报未决',
    claim_expense_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '理赔费用准备金',
    outstanding_claim_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '未决赔款准备金',
    total_accounting_reserve DECIMAL(18,10) DEFAULT 0 COMMENT '会计准备金合计',
    reinsurance_unearned DECIMAL(18,10) DEFAULT 0 COMMENT '应收分保未到期责任准备金',
    reinsurance_reported DECIMAL(18,10) DEFAULT 0 COMMENT '应收分保已报未决',
    reinsurance_unreported DECIMAL(18,10) DEFAULT 0 COMMENT '应收分保未报未决',
    reinsurance_claim_total DECIMAL(18,10) DEFAULT 0 COMMENT '应收分保未决合计',
    reinsurance_total DECIMAL(18,10) DEFAULT 0 COMMENT '应收分保合计',
    lapsed_policy_value DECIMAL(18,10) DEFAULT 0 COMMENT '失效保单现价',
    fractional_month_dividend DECIMAL(18,10) DEFAULT 0 COMMENT '零头月红利',
    unpaid_dividend DECIMAL(18,10) DEFAULT 0 COMMENT '应付未付红利',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_accounting_reserve_unique (accounting_period, actuarial_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会计准备金明细表';

-- 6. 法定准备金预测表(TB0006)
CREATE TABLE t_base_statutory_reserve_forecast (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    business_type VARCHAR(20) NOT NULL COMMENT '业务类型',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    actuarial_code VARCHAR(20) NOT NULL COMMENT '精算代码',
    business_code VARCHAR(20) NOT NULL COMMENT '业务代码',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    design_type VARCHAR(50) NOT NULL COMMENT '设计类型',
    short_term_flag CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否中短：Y-是，N-否',
    statutory_reserve_t1 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T1',
    statutory_reserve_t2 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T2',
    statutory_reserve_t3 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T3',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_statutory_forecast_unique (business_type, accounting_period, actuarial_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='法定准备金预测表';

-- 7. 分产品统计表(TB0007)
CREATE TABLE t_cost_product_statistics (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    scenario_name VARCHAR(50) NOT NULL COMMENT '情景名称',
    business_type VARCHAR(20) NOT NULL COMMENT '业务类型',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    actuarial_code VARCHAR(20) NOT NULL COMMENT '精算代码',
    business_code VARCHAR(20) NOT NULL COMMENT '业务代码',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    term_type CHAR(1) NOT NULL DEFAULT 'L' COMMENT '长短期标识：L-长期，S-短期',
    design_type VARCHAR(50) NOT NULL COMMENT '设计类型',
    short_term_flag CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否中短：Y-是，N-否',
    statutory_reserve_t0 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T0',
    statutory_reserve_t1 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T1',
    statutory_reserve_t2 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T2',
    statutory_reserve_t3 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T3',
    fund_cost_rate_t0 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T0',
    fund_cost_rate_t1 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T1',
    fund_cost_rate_t2 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T2',
    fund_cost_rate_t3 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T3',
    guaranteed_cost_rate_t0 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T0',
    guaranteed_cost_rate_t1 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T1',
    guaranteed_cost_rate_t2 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T2',
    guaranteed_cost_rate_t3 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T3',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_product_statistics_unique (scenario_name, business_type, accounting_period, actuarial_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分产品统计表';

-- 8. 分业务类型汇总表(TB0008)
CREATE TABLE t_cost_business_type_summary (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    scenario_name VARCHAR(50) NOT NULL COMMENT '情景名称',
    business_type VARCHAR(20) NOT NULL COMMENT '业务类型',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    design_type VARCHAR(50) NOT NULL COMMENT '设计类型',
    statutory_reserve_t0 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T0',
    statutory_reserve_t1 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T1',
    statutory_reserve_t2 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T2',
    statutory_reserve_t3 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T3',
    fund_cost_rate_t0 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T0',
    fund_cost_rate_t1 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T1',
    fund_cost_rate_t2 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T2',
    fund_cost_rate_t3 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T3',
    guaranteed_cost_rate_t0 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T0',
    guaranteed_cost_rate_t1 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T1',
    guaranteed_cost_rate_t2 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T2',
    guaranteed_cost_rate_t3 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T3',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_business_summary_unique (scenario_name, business_type, accounting_period, design_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分业务类型汇总表';

-- 9. 分账户汇总表(TB0009)
CREATE TABLE t_cost_account_summary (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    scenario_name VARCHAR(50) NOT NULL COMMENT '情景名称',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    design_type VARCHAR(50) NOT NULL COMMENT '设计类型',
    statutory_reserve_t0 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T0',
    statutory_reserve_t1 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T1',
    statutory_reserve_t2 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T2',
    statutory_reserve_t3 DECIMAL(18,10) DEFAULT 0 COMMENT '法定准备金T3',
    fund_cost_rate_t0 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T0',
    fund_cost_rate_t1 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T1',
    fund_cost_rate_t2 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T2',
    fund_cost_rate_t3 DECIMAL(10,10) DEFAULT 0 COMMENT '资金成本率T3',
    guaranteed_cost_rate_t0 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T0',
    guaranteed_cost_rate_t1 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T1',
    guaranteed_cost_rate_t2 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T2',
    guaranteed_cost_rate_t3 DECIMAL(10,10) DEFAULT 0 COMMENT '保证成本率T3',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_account_summary_unique (scenario_name, accounting_period, design_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分账户汇总表';

-- 10. 分产品有效成本率表(TB0010)
CREATE TABLE t_cost_product_effective_rate (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    actuarial_code VARCHAR(20) NOT NULL COMMENT '精算代码',
    design_type VARCHAR(50) NOT NULL COMMENT '设计类型',
    short_term_flag CHAR(1) NOT NULL DEFAULT 'N' COMMENT '是否中短：Y-是，N-否',
    business_code VARCHAR(20) NOT NULL COMMENT '业务代码',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    effective_cost_rate DECIMAL(10,10) DEFAULT 0 COMMENT '产品有效成本率',
    cash_flow_set TEXT NOT NULL COMMENT '现金流值集',
    create_by VARCHAR(64) COMMENT '创建人员',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人员',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注信息',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_product_effective_rate_unique (accounting_period, actuarial_code, design_type, short_term_flag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分产品有效成本率表';

-- 11. 分账户有效成本率表(TB0011)
CREATE TABLE t_cost_account_effective_rate (
    id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    accounting_period VARCHAR(6) NOT NULL COMMENT '账期',
    design_type VARCHAR(50) NOT NULL COMMENT '设计类型',
    effective_cost_rate DECIMAL(10,10) DEFAULT 0 COMMENT '账户有效成本率',
    cash_flow_set TEXT NOT NULL COMMENT '现金流值集',
    create_by VARCHAR(64) COMMENT '创建人员',
    create_time DATETIME COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新人员',
    update_time DATETIME COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注信息',
    is_del TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
    PRIMARY KEY (id),
    UNIQUE KEY idx_account_effective_rate_unique (accounting_period, design_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分账户有效成本率表';

